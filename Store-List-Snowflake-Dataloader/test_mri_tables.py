#!/usr/bin/env python3
"""
MRI Tables Diagnostic Test Script

This script tests the MRI tables to understand what data is available
and why the lease exceptions report might not be finding current leases.

Usage:
    ./runner.sh -e dev -c test_mri_tables.py

Environment Variables Used:
- DATABASE_CSM_DATABASE: Database name
- DATABASE_MRI_SCHEMA: Schema name (should be "MRI")
- DATABASE_WAREHOUSE: Warehouse name
- DATABASE_ROLE: Role name

- <PERSON> - 7/10/2025
"""

import os
import pandas as pd
from datetime import datetime
from libs.snowflake_helper import SnowflakeHelper


def print_section(title):
    """Print a formatted section header"""
    print("\n" + "="*80)
    print(f" {title}")
    print("="*80)


def print_subsection(title):
    """Print a formatted subsection header"""
    print(f"\n{title}")
    print("-" * 60)


def execute_and_display_query(sf, query_name, query, limit_rows=10):
    """Execute a query and display results in a readable format"""
    print_subsection(query_name)
    
    try:
        print(f"Query: {query.strip()}")
        print()
        
        results = sf.execute_snowflake_query(
            query=query,
            print_query=False,
            pull_only_one_record=False
        )
        
        if results:
            if isinstance(results, list) and len(results) > 0:
                # Convert to DataFrame for better display
                df = pd.DataFrame(results)
                
                # Display results
                if len(df) <= limit_rows:
                    print(df.to_string(index=False))
                else:
                    print(f"Showing first {limit_rows} of {len(df)} rows:")
                    print(df.head(limit_rows).to_string(index=False))
                    print(f"... ({len(df) - limit_rows} more rows)")
            else:
                print(f"Results: {results}")
        else:
            print("No results returned")
            
        return results
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return None


def main():
    """Main diagnostic function"""
    print_section("MRI TABLES DIAGNOSTIC TEST")
    
    try:
        # Initialize SnowflakeHelper
        sf = SnowflakeHelper()
        
        # Display connection information
        print(f"Connected to Snowflake successfully")
        print(f"Connection type: {type(sf.conn).__name__}")
        
        # Display environment variables
        print_subsection("Environment Configuration")
        env_vars = [
            'DATABASE_CSM_DATABASE',
            'DATABASE_MRI_SCHEMA', 
            'DATABASE_WAREHOUSE',
            'DATABASE_ROLE'
        ]
        
        for var in env_vars:
            value = os.environ.get(var, 'NOT SET')
            print(f"{var}: {value}")
        
        # Get current session info
        print_subsection("Current Snowflake Session")
        session_query = "SELECT CURRENT_DATABASE(), CURRENT_SCHEMA(), CURRENT_WAREHOUSE(), CURRENT_ROLE()"
        session_results = execute_and_display_query(sf, "Session Info", session_query)
        
        # Test 1: Basic table counts
        print_section("TABLE EXISTENCE AND COUNTS")
        
        table_queries = [
            ("MRI.LEAS (Main Lease Table)", "SELECT COUNT(*) as total_leases FROM MRI.LEAS"),
            ("MRI.BLDG (Buildings)", "SELECT COUNT(*) as total_buildings FROM MRI.BLDG"),
            ("MRI.MNGR (Managers)", "SELECT COUNT(*) as total_managers FROM MRI.MNGR"),
            ("MRI.MOCCP (Occupancy)", "SELECT COUNT(*) as total_occupancy_records FROM MRI.MOCCP")
        ]
        
        for table_name, query in table_queries:
            execute_and_display_query(sf, table_name, query)
        
        # Test 2: Occupancy Status Analysis
        print_section("OCCUPANCY STATUS ANALYSIS")
        
        status_query = """
        SELECT 
            OCCPSTAT,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
        FROM MRI.LEAS 
        WHERE OCCPSTAT IS NOT NULL
        GROUP BY OCCPSTAT 
        ORDER BY count DESC
        """
        execute_and_display_query(sf, "Occupancy Status Distribution", status_query)
        
        # Test 3: Sample lease records
        print_section("SAMPLE LEASE RECORDS")
        
        sample_query = """
        SELECT 
            BLDGID,
            SUITID,
            LEASID,
            OCCPSTAT,
            OCCPNAME,
            TO_CHAR(RENTSTRT, 'YYYY-MM-DD') as RENT_START,
            TO_CHAR(STOPBILLDATE, 'YYYY-MM-DD') as STOP_BILL_DATE,
            TO_CHAR(VACATE, 'YYYY-MM-DD') as VACATE_DATE,
            TO_CHAR(EXPIR, 'YYYY-MM-DD') as EXPIRE_DATE
        FROM MRI.LEAS 
        ORDER BY BLDGID, SUITID, LEASID
        LIMIT 15
        """
        execute_and_display_query(sf, "Sample Lease Records", sample_query, limit_rows=15)
        
        # Test 4: Date fields analysis
        print_section("DATE FIELDS ANALYSIS")
        
        date_analysis_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(RENTSTRT) as has_rent_start,
            COUNT(STOPBILLDATE) as has_stop_bill_date,
            COUNT(VACATE) as has_vacate_date,
            COUNT(EXPIR) as has_expire_date,
            TO_CHAR(MIN(RENTSTRT), 'YYYY-MM-DD') as min_rent_start,
            TO_CHAR(MAX(RENTSTRT), 'YYYY-MM-DD') as max_rent_start,
            TO_CHAR(MIN(EXPIR), 'YYYY-MM-DD') as min_expire_date,
            TO_CHAR(MAX(EXPIR), 'YYYY-MM-DD') as max_expire_date
        FROM MRI.LEAS
        """
        execute_and_display_query(sf, "Date Fields Summary", date_analysis_query)
        
        # Test 5: Past dates analysis by status
        print_section("PAST DATES ANALYSIS BY STATUS")
        
        past_dates_query = """
        SELECT 
            OCCPSTAT,
            COUNT(*) as total_count,
            COUNT(CASE WHEN STOPBILLDATE < CURRENT_DATE THEN 1 END) as past_stop_bill,
            COUNT(CASE WHEN VACATE < CURRENT_DATE THEN 1 END) as past_vacate,
            COUNT(CASE WHEN EXPIR < CURRENT_DATE THEN 1 END) as past_expire,
            COUNT(CASE WHEN (STOPBILLDATE < CURRENT_DATE OR VACATE < CURRENT_DATE OR EXPIR < CURRENT_DATE) THEN 1 END) as any_past_date
        FROM MRI.LEAS
        GROUP BY OCCPSTAT
        ORDER BY total_count DESC
        """
        execute_and_display_query(sf, "Past Dates by Occupancy Status", past_dates_query)
        
        # Test 6: Check for 'C' status specifically
        print_section("CURRENT STATUS ('C') INVESTIGATION")
        
        current_status_queries = [
            ("Exact 'C' Status Count", "SELECT COUNT(*) as current_leases FROM MRI.LEAS WHERE OCCPSTAT = 'C'"),
            ("Case Insensitive 'C' Status", "SELECT COUNT(*) as current_leases FROM MRI.LEAS WHERE UPPER(OCCPSTAT) = 'C'"),
            ("'C' with Whitespace", "SELECT COUNT(*) as current_leases FROM MRI.LEAS WHERE TRIM(OCCPSTAT) = 'C'"),
            ("All Status Values (Unique)", "SELECT DISTINCT OCCPSTAT, LENGTH(OCCPSTAT) as length FROM MRI.LEAS ORDER BY OCCPSTAT")
        ]
        
        for query_name, query in current_status_queries:
            execute_and_display_query(sf, query_name, query)
        
        # Test 7: Check if there are any records that would match the original criteria with different status
        print_section("POTENTIAL MATCHES WITH DIFFERENT STATUS")
        
        potential_matches_query = """
        SELECT 
            OCCPSTAT,
            COUNT(*) as count
        FROM MRI.LEAS
        WHERE (
            STOPBILLDATE < CURRENT_DATE
            OR VACATE < CURRENT_DATE
            OR EXPIR < CURRENT_DATE
        )
        GROUP BY OCCPSTAT
        ORDER BY count DESC
        """
        execute_and_display_query(sf, "Records with Past Dates (Any Status)", potential_matches_query)
        
        print_section("DIAGNOSTIC COMPLETE")
        print("Review the results above to understand:")
        print("1. What occupancy status codes actually exist in your data")
        print("2. Whether there are leases with past dates (just not with 'C' status)")
        print("3. The overall data structure and content")
        
        return True
        
    except Exception as e:
        print(f"FATAL ERROR in diagnostic script: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    """Entry point for the diagnostic script"""
    import sys
    
    print(f"Starting MRI Tables Diagnostic Test")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = main()
    
    if success:
        print(f"\n✅ Diagnostic completed successfully!")
    else:
        print(f"\n❌ Diagnostic failed!")
    
    sys.exit(0 if success else 1)
