"""
Process Name: MRI Lease Exceptions Past Inactive Dates
R-Script Name: LEGACY_MRI_Exceptions-Lease_C_past_inactive_dates.R

Identifies MRI lease records that are marked as "Current" but have passed critical dates
(stop billing, vacate, or expiration dates), then emails an Excel report to property management.

Environment Variables:
- LOG_OUTPUT_DIR: Directory for log files (default: ./logs)
- REPORT_OUTPUT_DIR: Directory for report files (default: ./reports)

- <PERSON> -6/26/2025
"""

import os
import pandas as pd
import uuid
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from libs.snowflake_helper import SnowflakeHelper
from libs import email_client


@dataclass
class ReportResult:
    """Data class to hold report generation results"""
    success: bool
    row_count: int
    file_path: Optional[str] = None
    error_message: Optional[str] = None


class MRILeaseExceptionsPastInactiveDates:
    """
    Class to handle MRI lease exceptions for current leases past inactive dates.
    Converts R script functionality to Python.
    """

    PROCESS_NAME = 'MRI Lease Exceptions-Inactive But Past Expiration Date (TEST)'
    PROCESS_TYPE = 'Exception Report'
    LOG_TO_DB = False

    R_SCRIPT_NAME = 'LEGACY_MRI_Exceptions-Lease_C_past_inactive_dates.R'

    LOG_SCHEMA = 'BATCH_AUDIT'
    LOG_TABLE = 'MOMS_EXECUTION_LOGS'

    # Email configuration
    NORM_RECIPIENTS = ['<EMAIL>']
    WARN_RECIPIENTS = ['<EMAIL>']
    TEST_RECIPIENTS = ['<EMAIL>']
    TEST_CC_RECIPIENTS = ['<EMAIL>']

    # Override email recipients (for testing)
    OVERRIDE_EMAIL_RECIPIENTS = None  # Set to list of emails to override recipients

    # Report configuration
    REPORT_FOLDER = 'LEGACY_MRI_Exceptions-Leases'

    def __init__(self, testing_emails: bool = False):
        """
        Initialize the MRI lease exceptions processor.

        Args:
            testing_emails: Whether to send test emails only
        """
        # Set up logging first
        self.setup_logging()

        self.sf = SnowflakeHelper()
        self.testing_emails = testing_emails
        self.log_buffer = []

        # Access SnowflakeHelper connection properties for advanced operations if needed
        self.cursor = self.sf.cs  # Snowflake cursor for direct SQL operations
        self.conn = self.sf.conn  # Snowflake connection object

        # Set timezone to Central
        os.environ['TZ'] = 'America/Chicago'

        # Report parameters
        self.query_date = datetime.now().strftime("%d-%b-%y")
        self.report_time = datetime.now().strftime("%H%M%S%Z")

        # Report criteria description
        self.report_criteria = (
            "<p><b>TEST MODE - Criteria for exceptions,</b> lease status = 'I' but one or more apply:"
            "<ul>"
            "<li>Past Stop Billing Date</li>"
            "<li>Past Vacate Date</li>"
            "<li>Past Expire Date</li>"
            "</ul></p>"
        )

        # Email signature
        self.norm_sig = (
            "<b><span style='font-weight:bold'>Steve Olson</span></b><br/>"
            "Sr. Analytics Mgr.<br/>"
            "<b><span style='font-weight:bold'>Highland Ventures, Ltd.</span></b><br/>"
            "2500 Lehigh Ave.<br/>"
            "Glenview, IL 60026<br/>"
            "Ph: 847/904-9043<br/></span></font>"
        )

        # Email configuration for new pattern
        self.report_name = self.PROCESS_NAME
        self.norm_recip = self.TEST_RECIPIENTS if testing_emails else self.NORM_RECIPIENTS
        self.gmail_reply_to = "<EMAIL>"

        # Set up report path
        self.setup_report_path()

        self.log_audit_in_db(
            log_msg=f"Beginning '{self.PROCESS_NAME}' routine",
            print_msg=True
        )

        # Log SnowflakeHelper integration status
        self.log_audit_in_db(
            log_msg=f"SnowflakeHelper initialized successfully. Connection: {type(self.conn).__name__}",
            print_msg=True
        )

    def setup_logging(self):
        """
        Set up comprehensive logging to both console and file.
        Creates a log file in the logs directory with timestamp.
        Log directory can be configured via LOG_OUTPUT_DIR environment variable.
        """
        # Get log directory from environment variable or use default
        log_dir = os.environ.get('LOG_OUTPUT_DIR', os.path.join(os.getcwd(), 'logs'))
        os.makedirs(log_dir, exist_ok=True)

        # Create log filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"MRI_lease_exceptions_{timestamp}.log"
        log_filepath = os.path.join(log_dir, log_filename)

        # Configure logging
        self.logger = logging.getLogger(self.PROCESS_NAME)
        self.logger.setLevel(logging.DEBUG)

        # Clear any existing handlers
        self.logger.handlers.clear()

        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # File handler - logs everything to file
        file_handler = logging.FileHandler(log_filepath, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

        # Console handler - logs INFO and above to console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # Store log file path for reference
        self.log_file_path = log_filepath

        # Log the initialization
        self.logger.info(f"Logging initialized. Log file: {log_filepath}")
        self.logger.info(f"Log directory: {log_dir}")
        self.logger.info(f"Process: {self.PROCESS_NAME}")
        self.logger.info(f"Testing emails: {getattr(self, 'testing_emails', 'Not set yet')}")

    def setup_report_path(self):
        """
        Set up the report output path.
        Report directory can be configured via REPORT_OUTPUT_DIR environment variable.
        """
        # Get report directory from environment variable or use default
        base_path = os.environ.get('REPORT_OUTPUT_DIR', os.path.join(os.getcwd(), 'reports'))
        self.report_path = os.path.join(base_path, self.REPORT_FOLDER)

        # Create directory if it doesn't exist
        os.makedirs(self.report_path, exist_ok=True)

        # Log the report path setup
        if hasattr(self, 'logger'):
            self.logger.info(f"Report directory: {self.report_path}")

    def log_audit_in_db(self, log_msg: str, log_type: str = 'Info', print_msg: bool = False,
                       print_data_list: bool = True, start_upload: bool = False,
                       process_type: Optional[str] = None, script_file_name: Optional[str] = None) -> bool:
        """
        Store log messages in a buffer and upload them in bulk when requested.
        Also logs to the file logger for comprehensive logging.

        Args:
            log_msg: Message to log
            log_type: Type of log (Info, Warning, Error, etc.)
            print_msg: Whether to print the log message
            print_data_list: Whether to print the data list
            start_upload: Whether to upload the collected logs
            process_type: Process type override
            script_file_name: Script name override

        Returns:
            Success status of the operation
        """
        try:
            # Log to file logger based on log type
            if hasattr(self, 'logger'):
                if log_type.lower() == 'error':
                    self.logger.error(log_msg)
                elif log_type.lower() == 'warning':
                    self.logger.warning(log_msg)
                elif log_type.lower() == 'debug':
                    self.logger.debug(log_msg)
                else:
                    self.logger.info(log_msg)

            # Print log message if requested (in addition to file logging)
            if print_msg:
                print(f"{self.PROCESS_NAME} - {log_type}: {log_msg}")

            # Create the log record and add to buffer
            uuid_str = str(uuid.uuid4())
            script_name = script_file_name or self.PROCESS_NAME
            process_type = process_type or self.PROCESS_TYPE
            rec_ins_date = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
            record = [uuid_str, script_name, log_type, process_type, log_msg, rec_ins_date]
            self.log_buffer.append(record)

            if print_data_list and hasattr(self, 'logger'):
                self.logger.debug(f"Added to log buffer. Current size: {len(self.log_buffer)}")

            if not self.LOG_TO_DB:
                return True

            # Upload logs if requested
            if start_upload and self.log_buffer:
                columns_list = ['BATCH_ID', 'CALLING_PROGRAM_NAME', 'LOG_TYPE', 'PROCESS_TYPE', 'LOG_MSG', 'RECORD_INSERTED_AT']

                try:
                    self.sf.bulk_insert(
                        columns_list=columns_list,
                        data_list=self.log_buffer,
                        database=os.environ.get('DATABASE_RAW_DATABASE', 'PROD_CSM_DB'),
                        schema=self.LOG_SCHEMA,
                        table=self.LOG_TABLE
                    )
                    print(f"Uploaded {len(self.log_buffer)} log entries in bulk")

                    # Clear the buffer after successful upload
                    self.log_buffer = []
                    return True

                except Exception as e:
                    print(f"Error uploading logs for {self.PROCESS_NAME}: {e}")
                    return False

            return True

        except Exception as e:
            error_msg = f"Error in log_audit_in_db for {self.PROCESS_NAME}: {e}"
            print(error_msg)
            if hasattr(self, 'logger'):
                self.logger.error(error_msg)
            return False

    def log_query_execution(self, query: str, result_count: int = None, execution_time: float = None):
        """
        Log detailed information about query execution.

        Args:
            query: The SQL query that was executed
            result_count: Number of rows returned (if available)
            execution_time: Query execution time in seconds (if available)
        """
        if hasattr(self, 'logger'):
            self.logger.info("="*80)
            self.logger.info("SNOWFLAKE QUERY EXECUTION")
            self.logger.info("="*80)
            self.logger.info(f"Query Purpose: Find MRI lease exceptions")
            if execution_time:
                self.logger.info(f"Execution Time: {execution_time:.2f} seconds")
            if result_count is not None:
                self.logger.info(f"Rows Returned: {result_count}")
            self.logger.debug("SQL Query:")
            self.logger.debug("-" * 40)
            # Log query in chunks to avoid line length issues
            for line in query.strip().split('\n'):
                self.logger.debug(line.strip())
            self.logger.debug("-" * 40)
            self.logger.info("="*80)

    def log_data_summary(self, data: pd.DataFrame, operation: str = "Data Processing"):
        """
        Log summary information about a DataFrame.

        Args:
            data: DataFrame to summarize
            operation: Description of the operation
        """
        if not hasattr(self, 'logger') or data is None or len(data) == 0:
            return

        self.logger.info(f"{operation} - Data Summary:")
        self.logger.info(f"  Total records: {len(data)}")
        self.logger.info(f"  Columns: {list(data.columns)}")

        # Log issue breakdown if ISSUE column exists
        if 'ISSUE' in data.columns:
            issue_counts = {}
            for issue in data['ISSUE']:
                if pd.notna(issue):
                    for issue_type in str(issue).split('; '):
                        issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1

            self.logger.info("  Issue breakdown:")
            for issue_type, count in issue_counts.items():
                self.logger.info(f"    - {issue_type}: {count} records")

        # Log building breakdown if BLDGID column exists
        if 'BLDGID' in data.columns:
            building_counts = data['BLDGID'].value_counts()
            self.logger.info(f"  Top 5 buildings with issues:")
            for building, count in building_counts.head(5).items():
                self.logger.info(f"    - {building}: {count} records")

    def log_execution_summary(self, start_time: datetime, success: bool, row_count: int,
                             file_path: str = None, error_message: str = None):
        """
        Log a comprehensive summary of the execution.

        Args:
            start_time: When the execution started
            success: Whether the execution was successful
            row_count: Number of rows processed
            file_path: Path to created file (if any)
            error_message: Error message (if any)
        """
        if not hasattr(self, 'logger'):
            return

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        self.logger.info("="*80)
        self.logger.info("EXECUTION SUMMARY")
        self.logger.info("="*80)
        self.logger.info(f"Process: {self.PROCESS_NAME}")
        self.logger.info(f"Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"Duration: {duration:.2f} seconds")
        self.logger.info(f"Success: {success}")
        self.logger.info(f"Records Processed: {row_count}")

        if file_path:
            self.logger.info(f"Output File: {file_path}")
        if error_message:
            self.logger.error(f"Error: {error_message}")

        self.logger.info(f"Log File: {getattr(self, 'log_file_path', 'Not available')}")
        self.logger.info("="*80)

    def get_lease_exceptions_query(self) -> str:
        """
        Get the SQL query to find lease exceptions.
        Converted from R script's Snowflake query.

        Returns:
            SQL query string
        """
        return """
        SELECT /* SNOWFLAKE VERSION */
        ARRAY_TO_STRING(ARRAY_CONSTRUCT_COMPACT(
            CASE WHEN LEAS.STOPBILLDATE < CURRENT_DATE THEN 'Stop Bill Date Past' END,
            CASE WHEN LEAS.VACATE < CURRENT_DATE THEN 'Vacate Date Past' END,
            CASE WHEN LEAS.EXPIR < CURRENT_DATE THEN 'Expire Date Past' END
        ),'; ') AS ISSUE
        , LEAS.BLDGID
        , LEAS.SUITID
        , MNGR.MNGRNAME AS "PROP MGR"
        , LEAS.LEASID
        , LEAS.OCCPSTAT AS "OCCUPANCY STATUS"
        , LEAS.OCCPNAME AS "TENANT NAME"
        , LEAS.GENCODE AS "GEN CODE"
        , TO_DATE(LEAS.RENTSTRT) AS "RENT START"
        , TO_DATE(LEAS.OCCUPNCY) AS "OCCUPANCY DATE"
        , TO_DATE(LEAS.STOPBILLDATE) AS "STOP BILL DATE"
        , TO_DATE(LEAS.VACATE) as "VACATE DATE"
        , TO_DATE(LEAS.EXPIR) as "EXPIRE DATE"
        , LEAS.TENTCAT AS "TENANT CATEGORY"
        FROM MRI.LEAS
        LEFT JOIN MRI.MOCCP ON LEAS.MOCCPID = MOCCP.MOCCPID
        LEFT JOIN MRI.BLDG ON LEAS.BLDGID = BLDG.BLDGID
        LEFT JOIN MRI.MNGR ON BLDG.MNGRID = MNGR.MNGRID
        WHERE LEAS.OCCPSTAT = 'I'
         AND LEAS.OCCPSTAT NOT IN ('P', 'C')
         AND (
            (
                (
                    LEAS.RENTSTRT <= CURRENT_DATE
                    OR LEAS.OCCUPNCY <= CURRENT_DATE
                    OR LEAS.EXECDATE <= CURRENT_DATE
                )
                AND (
                        LEAS.STOPBILLDATE IS NULL
                        OR LEAS.STOPBILLDATE < CURRENT_DATE
                        OR (
                                LEAS.STOPBILLDATE >= CURRENT_DATE
                                AND COALESCE(LEAS.VACATE,LEAS.EXPIR) < CURRENT_DATE
                            )
                )
                AND COALESCE(LEAS.VACATE,LEAS.EXPIR)< CURRENT_DATE
            )
        )
        ORDER BY LEAS.BLDGID, LEAS.SUITID, LEAS.LEASID
        """

    def execute_lease_exceptions_query(self) -> Optional[pd.DataFrame]:
        """
        Execute the lease exceptions query and return results.

        Returns:
            DataFrame with query results or None if error
        """
        try:
            self.log_audit_in_db(
                log_msg="Executing lease exceptions query",
                print_msg=True
            )

            query = self.get_lease_exceptions_query()

            # Log schema information for debugging to both console and log file
            self.logger.info("="*80)
            self.logger.info("SNOWFLAKE CONNECTION DEBUG INFORMATION")
            self.logger.info("="*80)

            # Get connection info from environment variables and connection
            database_env = os.environ.get("DATABASE_CSM_DATABASE", "Not set")
            schema_env = os.environ.get("DATABASE_MRI_SCHEMA", "Not set")
            warehouse_env = os.environ.get("DATABASE_WAREHOUSE", "Not set")
            role_env = os.environ.get("DATABASE_ROLE", "Not set")

            self.logger.info(f"Environment DATABASE_CSM_DATABASE: {database_env}")
            self.logger.info(f"Environment DATABASE_MRI_SCHEMA: {schema_env}")
            self.logger.info(f"Environment DATABASE_WAREHOUSE: {warehouse_env}")
            self.logger.info(f"Environment DATABASE_ROLE: {role_env}")

            # Try to get current session info from Snowflake
            try:
                session_info_query = "SELECT CURRENT_DATABASE(), CURRENT_SCHEMA(), CURRENT_WAREHOUSE(), CURRENT_ROLE()"
                session_results = self.sf.execute_snowflake_query(
                    query=session_info_query,
                    print_query=False,
                    pull_only_one_record=True
                )
                if session_results:
                    self.logger.info(f"Current Snowflake Session - Database: {session_results[0][0]}")
                    self.logger.info(f"Current Snowflake Session - Schema: {session_results[0][1]}")
                    self.logger.info(f"Current Snowflake Session - Warehouse: {session_results[0][2]}")
                    self.logger.info(f"Current Snowflake Session - Role: {session_results[0][3]}")
            except Exception as e:
                self.logger.warning(f"Could not retrieve session info: {e}")

            self.logger.info("="*80)
            self.logger.info("QUERY SCHEMA USAGE:")
            self.logger.info("The query uses the following schema-qualified tables:")
            self.logger.info("- MRI.LEAS (main lease table)")
            self.logger.info("- MRI.MOCCP (occupancy table)")
            self.logger.info("- MRI.BLDG (building table)")
            self.logger.info("- MRI.MNGR (manager table)")
            self.logger.info("="*80)

            # Log query details
            self.log_query_execution(query)

            # Track execution time
            start_time = datetime.now()

            # First, let's check if there's any data in the MRI.LEAS table
            diagnostic_query = "SELECT COUNT(*) as total_leases FROM MRI.LEAS"
            self.logger.info("Running diagnostic query to check MRI.LEAS table...")
            diagnostic_results = self.sf.execute_snowflake_query(
                query=diagnostic_query,
                print_query=True,
                pull_only_one_record=False
            )
            self.logger.info(f"Total leases in MRI.LEAS: {diagnostic_results}")

            # Check for leases with status 'C'
            status_query = "SELECT COUNT(*) as current_leases FROM MRI.LEAS WHERE OCCPSTAT = 'I'"
            status_results = self.sf.execute_snowflake_query(
                query=status_query,
                print_query=True,
                pull_only_one_record=False
            )
            self.logger.info(f"Current leases (OCCPSTAT='C'): {status_results}")

            # Check for leases with past dates (simplified version of main query)
            past_dates_query = """
            SELECT COUNT(*) as leases_with_past_dates
            FROM MRI.LEAS
            WHERE OCCPSTAT = 'C'
            AND (
                LEAS.STOPBILLDATE < CURRENT_DATE
                OR LEAS.VACATE < CURRENT_DATE
                OR LEAS.EXPIR < CURRENT_DATE
            )
            """
            past_dates_results = self.sf.execute_snowflake_query(
                query=past_dates_query,
                print_query=True,
                pull_only_one_record=False
            )
            self.logger.info(f"Current leases with past dates: {past_dates_results}")

            # Check recent data updates
            recent_updates_query = """
            SELECT
                COUNT(*) as total_records,
                MAX(CASE WHEN LEAS.STOPBILLDATE IS NOT NULL THEN LEAS.STOPBILLDATE END) as max_stop_bill_date,
                MAX(CASE WHEN LEAS.VACATE IS NOT NULL THEN LEAS.VACATE END) as max_vacate_date,
                MAX(CASE WHEN LEAS.EXPIR IS NOT NULL THEN LEAS.EXPIR END) as max_expire_date
            FROM MRI.LEAS
            WHERE OCCPSTAT = 'C'
            """
            recent_updates_results = self.sf.execute_snowflake_query(
                query=recent_updates_query,
                print_query=True,
                pull_only_one_record=False
            )
            self.logger.info(f"Current leases date analysis: {recent_updates_results}")

            # Execute main query using SnowflakeHelper
            query_results = self.sf.execute_snowflake_query(
                query=query,
                print_query=True,
                pull_only_one_record=False
            )
            print(f"This is the Query Results: {query_results}")

            # Log query results to file
            with open("query_results_log.txt", "w") as f:
                f.write(f"Query Results:\n{query_results}\n")
                f.write(f"Diagnostic - Total leases: {diagnostic_results}\n")
                f.write(f"Diagnostic - Current leases: {status_results}\n")
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()

            if not query_results:
                self.log_audit_in_db(
                    log_msg="No data returned from lease exceptions query",
                    log_type='Warning',
                    print_msg=True
                )
                self.log_query_execution(query, result_count=0, execution_time=execution_time)
                return pd.DataFrame()

            # Convert to DataFrame
            df = pd.DataFrame(query_results)

            # Clean up string columns (remove trailing spaces)
            for col in df.select_dtypes(include=['object']).columns:
                df[col] = df[col].astype(str).str.rstrip()

            # Log detailed results
            self.log_query_execution(query, result_count=len(df), execution_time=execution_time)
            self.log_data_summary(df, "Query Results")

            self.log_audit_in_db(
                log_msg=f"Query returned {len(df)} rows in {execution_time:.2f} seconds",
                print_msg=True
            )

            return df

        except Exception as e:
            error_msg = f"Error executing lease exceptions query: {str(e)}"
            self.log_audit_in_db(
                log_msg=error_msg,
                log_type='Error',
                print_msg=True
            )
            if hasattr(self, 'logger'):
                self.logger.exception("Full exception details:")
            return None

    def create_excel_report(self, data: pd.DataFrame, filename: str) -> Optional[str]:
        """
        Create an Excel report with the lease exceptions data using pandas.

        Args:
            data: DataFrame containing the lease exceptions
            filename: Name of the Excel file to create

        Returns:
            Full path to the created file or None if error
        """
        try:
            file_path = os.path.join(self.report_path, filename)

            self.log_audit_in_db(
                log_msg=f"Creating Excel report: {filename}",
                print_msg=True
            )

            # Create Excel writer with xlsxwriter engine for better formatting
            with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                # Write data to Excel
                data.to_excel(writer, sheet_name=self.query_date, index=False)

                # Get the workbook and worksheet objects
                workbook = writer.book
                worksheet = writer.sheets[self.query_date]

                # Define header format
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'vcenter',
                    'align': 'center',
                    'fg_color': '#D6D6D6',
                    'border': 1,
                    'font_name': 'Arial Narrow',
                    'font_size': 12
                })

                # Apply header formatting
                for col_num, value in enumerate(data.columns.values):
                    worksheet.write(0, col_num, value, header_format)

                # Set column widths based on content
                column_widths = {
                    'ISSUE': min(39, max(24, max(len(str(val)) for val in data.iloc[:, 0]) if len(data) > 0 else 24)),
                    'BLDGID': 8,
                    'SUITID': 8,
                    'PROP MGR': 22,
                    'LEASID': 8.5,
                    'OCCUPANCY STATUS': 7.5,
                    'TENANT NAME': min(36, max(21, max(len(str(val)) for val in data.iloc[:, 6]) if len(data) > 0 else 21)),
                    'GEN CODE': 7.5,
                    'RENT START': 10.5,
                    'OCCUPANCY DATE': 10.5,
                    'STOP BILL DATE': 10.5,
                    'VACATE DATE': 10.5,
                    'EXPIRE DATE': 10.5,
                    'TENANT CATEGORY': 9.5
                }

                # Apply column widths
                for col_idx, column in enumerate(data.columns):
                    if column in column_widths:
                        worksheet.set_column(col_idx, col_idx, column_widths[column])
                    else:
                        worksheet.set_column(col_idx, col_idx, 15)  # Default width

                # Freeze the header row
                worksheet.freeze_panes(1, 0)

                # Add auto filter
                worksheet.autofilter(0, 0, len(data), len(data.columns) - 1)

            self.log_audit_in_db(
                log_msg=f"Excel report created successfully: {file_path}",
                print_msg=True
            )

            return file_path

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f"Error creating Excel report: {str(e)}",
                log_type='Error',
                print_msg=True
            )
            return None



    def create_excel_file(self, data: pd.DataFrame, excel_file_path: str, column_widths: dict = None) -> bool:
        """
        Create an Excel file with the lease exceptions data.

        Args:
            data: DataFrame containing the lease exceptions
            excel_file_path: Full path where to save the Excel file
            column_widths: Dictionary of column widths (optional)

        Returns:
            True if file created successfully, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg=f"Creating Excel file: {excel_file_path}",
                print_msg=True
            )

            # Create Excel writer with xlsxwriter engine for better formatting
            with pd.ExcelWriter(excel_file_path, engine='xlsxwriter') as writer:
                # Write data to Excel
                data.to_excel(writer, sheet_name=self.query_date, index=False)

                # Get the workbook and worksheet objects
                workbook = writer.book
                worksheet = writer.sheets[self.query_date]

                # Define header format
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'vcenter',
                    'align': 'center',
                    'fg_color': '#D6D6D6',
                    'border': 1,
                    'font_name': 'Arial Narrow',
                    'font_size': 12
                })

                # Apply header formatting
                for col_num, value in enumerate(data.columns.values):
                    worksheet.write(0, col_num, value, header_format)

                # Set column widths
                if column_widths:
                    for col_idx, column in enumerate(data.columns):
                        if column in column_widths:
                            worksheet.set_column(col_idx, col_idx, column_widths[column])
                        else:
                            worksheet.set_column(col_idx, col_idx, 15)  # Default width
                else:
                    # Use default column widths
                    default_column_widths = {
                        'ISSUE': min(39, max(24, max(len(str(val)) for val in data.iloc[:, 0]) if len(data) > 0 else 24)),
                        'BLDGID': 8,
                        'SUITID': 8,
                        'PROP MGR': 22,
                        'LEASID': 8.5,
                        'OCCUPANCY STATUS': 7.5,
                        'TENANT NAME': min(36, max(21, max(len(str(val)) for val in data.iloc[:, 6]) if len(data) > 0 else 21)),
                        'GEN CODE': 7.5,
                        'RENT START': 10.5,
                        'OCCUPANCY DATE': 10.5,
                        'STOP BILL DATE': 10.5,
                        'VACATE DATE': 10.5,
                        'EXPIRE DATE': 10.5,
                        'TENANT CATEGORY': 9.5
                    }

                    for col_idx, column in enumerate(data.columns):
                        if column in default_column_widths:
                            worksheet.set_column(col_idx, col_idx, default_column_widths[column])
                        else:
                            worksheet.set_column(col_idx, col_idx, 15)  # Default width

                # Freeze the header row
                worksheet.freeze_panes(1, 0)

                # Add auto filter
                worksheet.autofilter(0, 0, len(data), len(data.columns) - 1)

            self.log_audit_in_db(
                log_msg=f"Excel file created successfully: {excel_file_path}",
                print_msg=True
            )

            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f"Error creating Excel file: {str(e)}",
                log_type='Error',
                print_msg=True
            )
            return False

    def create_email_body(self, data: pd.DataFrame, attachment_path: str) -> str:
        """
        Create the HTML email body with summary data.

        Args:
            data: DataFrame containing the lease exceptions
            attachment_path: Path to the Excel attachment

        Returns:
            HTML email body string
        """
        print("DEBUG: Starting create_email_body")
        # Select columns for email body table (subset of full data)
        email_columns = ['ISSUE', 'BLDGID', 'SUITID', 'PROP MGR', 'TENANT NAME',
                        'STOP BILL DATE', 'VACATE DATE', 'EXPIRE DATE']

        email_data = data[email_columns].copy()
        print("DEBUG: Email data copied")

        # Format date columns for display
        print("DEBUG: Starting date formatting")
        date_columns = ['STOP BILL DATE', 'VACATE DATE', 'EXPIRE DATE']
        for col in date_columns:
            if col in email_data.columns:
                print(f"DEBUG: Formatting column {col}")
                # Handle datetime.date objects, NaT values, and string dates
                def format_date(x):
                    if pd.isna(x) or x is None:
                        return ''
                    elif hasattr(x, 'strftime'):
                        try:
                            return x.strftime('%m/%d/%y')
                        except (ValueError, AttributeError):
                            return ''
                    else:
                        try:
                            parsed_date = pd.to_datetime(x, errors='coerce')
                            if pd.isna(parsed_date):
                                return ''
                            return parsed_date.strftime('%m/%d/%y')
                        except:
                            return ''

                email_data[col] = email_data[col].apply(format_date)
                print(f"DEBUG: Completed formatting column {col}")
        print("DEBUG: Date formatting completed")

        # Create HTML table if data is not too large
        if len(email_data) <= 20:
            table_html = email_data.to_html(
                index=False,
                table_id="exceptions_table",
                classes="table table-striped",
                border=2,
                escape=False
            )

            body_table = (
                f"<p>The info below contains MRI data from yesterday that "
                f"may need updating. <b>See attached Excel file for full details.</b></p>"
                f"<p>{table_html}</p>"
            )
        else:
            body_table = (
                f"<p><strong><em>There are {len(email_data)} results, "
                f"see attached file for all.</em></strong></p>"
            )

        # Construct full email body
        body_text = (
            f"<p><h2>REPORT: {self.PROCESS_NAME}</h2></p>"
            f"{self.report_criteria}"
            f"{body_table}"
            f"<br/>"
            f"{self.norm_sig}"
        )

        return body_text

    def send_email_report(self, data: pd.DataFrame, attachment_path: str) -> bool:
        """
        Send the email report with Excel attachment.

        Args:
            data: DataFrame containing the lease exceptions
            attachment_path: Path to the Excel file to attach

        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            self.log_audit_in_db(
                log_msg="Preparing to send email report",
                print_msg=True
            )

            # Determine recipients
            recipients = self.TEST_RECIPIENTS if self.testing_emails else self.NORM_RECIPIENTS

            # Create email body
            self.log_audit_in_db(
                log_msg="Starting email body creation",
                print_msg=True
            )
            body = self.create_email_body(data, attachment_path)
            self.log_audit_in_db(
                log_msg="Email body creation completed",
                print_msg=True
            )

            if self.testing_emails:
                body = f"<p><b>TEST EMAIL (normal recipients: {', '.join(self.NORM_RECIPIENTS)})</b></p>{body}"

            # Send email using the new simplified send_email function
            email_client.send_email(
                recipient=recipients,
                subject=self.PROCESS_NAME,
                body=body,
                attachments=[attachment_path] if attachment_path else [],
                replyto="<EMAIL>"
            )

            self.log_audit_in_db(
                log_msg=f"Email report sent successfully to: {', '.join(recipients)}",
                print_msg=True
            )

            return True

        except Exception as e:
            self.log_audit_in_db(
                log_msg=f"Error sending email report: {str(e)}",
                log_type='Error',
                print_msg=True
            )
            return False

    def check_data_rows(self, data: Optional[pd.DataFrame], min_rows: int = 1) -> Tuple[bool, int, str]:
        """
        Check if data meets minimum row requirements.

        Args:
            data: DataFrame to check
            min_rows: Minimum number of rows required

        Returns:
            Tuple of (success_status, row_count, status_message)
        """
        if data is None:
            return False, 0, f"{self.PROCESS_NAME}: NO RESULTS"

        if not isinstance(data, pd.DataFrame):
            return False, 0, f"{self.PROCESS_NAME}: NO RESULTS"

        row_count = len(data)

        if row_count >= min_rows:
            return True, row_count, f"{self.PROCESS_NAME}: COMPLETE"
        else:
            return False, row_count, f"{self.PROCESS_NAME}: INCOMPLETE RESULTS"

    def run_report(self) -> ReportResult:
        """
        Main method to execute the lease exceptions report.

        Returns:
            ReportResult with execution status and details
        """
        start_time = datetime.now()

        try:
            self.log_audit_in_db(
                log_msg="Starting lease exceptions report execution",
                print_msg=True
            )

            if hasattr(self, 'logger'):
                self.logger.info("="*80)
                self.logger.info("MRI LEASE EXCEPTIONS REPORT EXECUTION STARTED")
                self.logger.info("="*80)
                self.logger.info(f"Process: {self.PROCESS_NAME}")
                self.logger.info(f"Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                self.logger.info(f"Testing Mode: {self.testing_emails}")

            # Execute the query
            data = self.execute_lease_exceptions_query()

            # Check if we have valid data
            success, row_count, status_msg = self.check_data_rows(data, min_rows=1)

            if not success:
                self.log_audit_in_db(
                    log_msg=status_msg,
                    log_type='Warning',
                    print_msg=True
                )
                self.log_execution_summary(start_time, False, row_count, error_message=status_msg)
                return ReportResult(
                    success=False,
                    row_count=row_count,
                    error_message=status_msg
                )

            self.log_audit_in_db(
                log_msg=f"Found {row_count} lease exceptions",
                print_msg=True
            )

            # Create Excel file
            filename = f"MRI_Lease_Exceptions-Current_Past_Exp_Date.xlsx"
            file_path = self.create_excel_report(data, filename)

            if not file_path:
                return ReportResult(
                    success=False,
                    row_count=row_count,
                    error_message="Failed to create Excel report"
                )

            # Send email report
            email_success = self.send_email_report(data, file_path)

            if not email_success:
                self.log_audit_in_db(
                    log_msg="Email sending failed, but Excel file was created",
                    log_type='Warning',
                    print_msg=True
                )

            # Upload any remaining logs
            self.log_audit_in_db(
                log_msg="Report execution completed successfully",
                print_msg=True,
                start_upload=True
            )

            # Log execution summary
            self.log_execution_summary(start_time, True, row_count, file_path)

            return ReportResult(
                success=True,
                row_count=row_count,
                file_path=file_path
            )

        except Exception as e:
            error_msg = f"Error in run_report: {str(e)}"
            self.log_audit_in_db(
                log_msg=error_msg,
                log_type='Error',
                print_msg=True,
                start_upload=True
            )

            # Log execution summary with error
            self.log_execution_summary(start_time, False, 0, error_message=error_msg)

            if hasattr(self, 'logger'):
                self.logger.exception("Full exception details:")

            return ReportResult(
                success=False,
                row_count=0,
                error_message=error_msg
            )


def main():
    """
    Main execution function for the MRI lease exceptions report.
    """
    # Configuration
    testing_emails = True  # Set to True for testing

    print(f"Starting {MRILeaseExceptionsPastInactiveDates.PROCESS_NAME}")
    print(f"Converted from R script: {MRILeaseExceptionsPastInactiveDates.R_SCRIPT_NAME}")
    print(f"Testing emails: {testing_emails}")
    print("-" * 60)

    try:
        # Create processor instance
        processor = MRILeaseExceptionsPastInactiveDates(
            testing_emails=testing_emails
        )

        # Run the report
        result = processor.run_report()

        # Print results
        if result.success:
            print(f"\n✅ Report completed successfully!")
            print(f"   - Found {result.row_count} lease exceptions")
            if result.file_path:
                print(f"   - Excel file created: {result.file_path}")
            print(f"   - Email sent to recipients")
            if hasattr(processor, 'log_file_path'):
                print(f"   - Detailed log file: {processor.log_file_path}")
        else:
            print(f"\n❌ Report failed!")
            print(f"   - Row count: {result.row_count}")
            if result.error_message:
                print(f"   - Error: {result.error_message}")
            if hasattr(processor, 'log_file_path'):
                print(f"   - Check log file for details: {processor.log_file_path}")

        return result.success

    except Exception as e:
        print(f"\n💥 Fatal error in main execution: {str(e)}")
        return False


if __name__ == "__main__":
    """
    Entry point for script execution.
    """
    import sys

    success = main()

    # Exit with appropriate code
    sys.exit(0 if success else 1)
