Query Results:
[{'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0002', 'SUITID': '3218', 'PROP MGR': '<PERSON><PERSON><PERSON>', 'LEASID': '000086', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #2-10/26/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(1979, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 31), 'VACATE DATE': datetime.date(2020, 10, 26), 'EXPIRE DATE': datetime.date(2020, 10, 26), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0002', 'SUITID': '3218', 'PROP MGR': '<PERSON><PERSON><PERSON>', 'LEASID': '001646', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': "Claygames, Inc - Darcy's Cafe & Slots", 'GEN CODE': 'NEW', 'RENT START': datetime.date(2021, 5, 11), 'OCCUPANCY DATE': datetime.date(2021, 2, 1), 'STOP BILL DATE': datetime.date(2022, 8, 12), 'VACATE DATE': datetime.date(2022, 8, 12), 'EXPIRE DATE': datetime.date(2022, 8, 12), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0002', 'SUITID': 'KIOSK', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '001152', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'HPWI #4304', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2018, 5, 24), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 8, 31), 'VACATE DATE': datetime.date(2022, 8, 11), 'EXPIRE DATE': datetime.date(2022, 8, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0010', 'SUITID': 'BLDG', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '000103', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #10-10/26/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(1989, 10, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 9), 'VACATE DATE': datetime.date(2020, 10, 9), 'EXPIRE DATE': datetime.date(2020, 10, 9), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0022', 'SUITID': 'BLDG', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '000106', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #22-10/26/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(1991, 11, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 31), 'VACATE DATE': datetime.date(2020, 10, 26), 'EXPIRE DATE': datetime.date(2020, 10, 26), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0077', 'SUITID': 'BLDG', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '000147', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #77', 'GEN CODE': 'NEW', 'RENT START': datetime.date(1996, 8, 30), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 2, 29), 'VACATE DATE': datetime.date(2020, 2, 20), 'EXPIRE DATE': datetime.date(2020, 2, 29), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': '0148', 'SUITID': 'BLDG', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '0148A0', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Dollar General #21083', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 1, 29), 'OCCUPANCY DATE': datetime.date(2019, 12, 1), 'STOP BILL DATE': datetime.date(2020, 9, 30), 'VACATE DATE': datetime.date(2020, 9, 30), 'EXPIRE DATE': datetime.date(2030, 2, 28), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0158', 'SUITID': 'A', 'PROP MGR': 'Tiffany Elam', 'LEASID': '000209', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #158 - 1/31/2021', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2000, 2, 17), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 1, 31), 'VACATE DATE': datetime.date(2021, 1, 31), 'EXPIRE DATE': datetime.date(2021, 1, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0158', 'SUITID': 'B', 'PROP MGR': 'Tiffany Elam', 'LEASID': '000933', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': "Marco's Pizza #3590", 'GEN CODE': 'NEW', 'RENT START': datetime.date(2015, 3, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 1, 31), 'VACATE DATE': datetime.date(2022, 1, 31), 'EXPIRE DATE': datetime.date(2022, 1, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0158', 'SUITID': 'B', 'PROP MGR': 'Tiffany Elam', 'LEASID': '001287', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Vireo Health, Inc', 'GEN CODE': 'NEW', 'RENT START': datetime.date(1999, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(1999, 1, 1), 'VACATE DATE': datetime.date(1999, 1, 1), 'EXPIRE DATE': datetime.date(1999, 1, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0176', 'SUITID': 'A', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '000226', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #176', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2000, 9, 25), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 1, 31), 'VACATE DATE': datetime.date(2021, 1, 31), 'EXPIRE DATE': datetime.date(2021, 1, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0294', 'SUITID': '7436B', 'PROP MGR': 'Jennifer Ray', 'LEASID': '000765', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': "Kahler LLC dba Shawn's Irish Tavern", 'GEN CODE': 'NEW', 'RENT START': datetime.date(2012, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 3, 31), 'VACATE DATE': datetime.date(2020, 3, 25), 'EXPIRE DATE': datetime.date(2021, 12, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0294', 'SUITID': '7448A', 'PROP MGR': 'Jennifer Ray', 'LEASID': '000313', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #294 - Closed 04/20', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2003, 2, 13), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 4, 30), 'VACATE DATE': datetime.date(2020, 4, 26), 'EXPIRE DATE': datetime.date(2018, 2, 12), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': '0344', 'SUITID': 'BLDG', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '0344A0', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Dollar General #21272', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2019, 9, 17), 'OCCUPANCY DATE': datetime.date(2019, 7, 6), 'STOP BILL DATE': datetime.date(2019, 12, 31), 'VACATE DATE': datetime.date(2019, 12, 31), 'EXPIRE DATE': datetime.date(2029, 9, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': '0361', 'SUITID': 'BLDG', 'PROP MGR': 'Tiffany Elam', 'LEASID': '000372', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #361-10/13/2020', 'GEN CODE': 'TER', 'RENT START': datetime.date(2004, 8, 19), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 13), 'VACATE DATE': datetime.date(2020, 10, 13), 'EXPIRE DATE': datetime.date(2099, 12, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0367', 'SUITID': 'ATM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001160', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'PNC Bank (ATM) #4639', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2018, 7, 13), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 8, 31), 'VACATE DATE': datetime.date(2021, 8, 31), 'EXPIRE DATE': datetime.date(2023, 7, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0367', 'SUITID': 'BLDG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '000373', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #367 - 6/30/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2004, 9, 7), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 6, 30), 'VACATE DATE': datetime.date(2020, 6, 30), 'EXPIRE DATE': datetime.date(2020, 5, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0367', 'SUITID': 'BLDG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001438', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'BrightView LLC - Cleveland', 'GEN CODE': 'TER', 'RENT START': datetime.date(2020, 10, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 13), 'VACATE DATE': datetime.date(2020, 10, 13), 'EXPIRE DATE': datetime.date(2020, 10, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0425', 'SUITID': 'A', 'PROP MGR': 'Jennifer Ray', 'LEASID': '000621', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': "Little Caesar's #2122", 'GEN CODE': 'NEW', 'RENT START': datetime.date(2009, 3, 31), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 6, 16), 'VACATE DATE': datetime.date(2021, 6, 16), 'EXPIRE DATE': datetime.date(2021, 6, 16), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0425', 'SUITID': 'B', 'PROP MGR': 'Jennifer Ray', 'LEASID': '000436', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #425-12/31/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2005, 8, 30), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 12, 31), 'VACATE DATE': datetime.date(2020, 12, 31), 'EXPIRE DATE': datetime.date(2020, 12, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': '0433', 'SUITID': 'A-102', 'PROP MGR': 'Matthew Semans', 'LEASID': '000898', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': "Marco's Pizza #3584", 'GEN CODE': 'NEW', 'RENT START': datetime.date(2014, 6, 29), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2024, 3, 18), 'VACATE DATE': datetime.date(2024, 3, 18), 'EXPIRE DATE': datetime.date(2099, 12, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past', 'BLDGID': '0433', 'SUITID': 'A-102', 'PROP MGR': 'Matthew Semans', 'LEASID': '002642', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': "Steve Buresh's Cookie Store", 'GEN CODE': 'NEW', 'RENT START': datetime.date(2024, 9, 11), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2024, 12, 11), 'EXPIRE DATE': datetime.date(2032, 9, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0433', 'SUITID': 'B-100', 'PROP MGR': 'Matthew Semans', 'LEASID': '000434', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #433-10/31/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2005, 8, 20), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 31), 'VACATE DATE': datetime.date(2020, 10, 31), 'EXPIRE DATE': datetime.date(2020, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0437', 'SUITID': 'A', 'PROP MGR': 'Matthew Semans', 'LEASID': '000447', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #437--10/31/20', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2005, 12, 2), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 31), 'VACATE DATE': datetime.date(2020, 10, 31), 'EXPIRE DATE': datetime.date(2020, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past', 'BLDGID': '0437', 'SUITID': 'A', 'PROP MGR': 'Matthew Semans', 'LEASID': '001838', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'BP Gas', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 9, 17), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2023, 4, 17), 'EXPIRE DATE': datetime.date(2042, 12, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past; Expire Date Past', 'BLDGID': '0437', 'SUITID': 'A', 'PROP MGR': 'Matthew Semans', 'LEASID': '002420', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'K & K Fireworks of Indiana, LLC', 'GEN CODE': 'LIC', 'RENT START': datetime.date(2023, 6, 9), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2023, 7, 9), 'EXPIRE DATE': datetime.date(2023, 7, 9), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past; Expire Date Past', 'BLDGID': '0437', 'SUITID': 'C', 'PROP MGR': 'Matthew Semans', 'LEASID': '000603', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Little Caesars #20560 R', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2008, 11, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2023, 10, 31), 'EXPIRE DATE': datetime.date(2023, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': '0437', 'SUITID': 'C', 'PROP MGR': 'Matthew Semans', 'LEASID': '002454', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Little Caesars', 'GEN CODE': 'REN', 'RENT START': datetime.date(2023, 11, 1), 'OCCUPANCY DATE': datetime.date(2023, 11, 1), 'STOP BILL DATE': datetime.date(2025, 2, 4), 'VACATE DATE': datetime.date(2025, 2, 4), 'EXPIRE DATE': datetime.date(2028, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0463', 'SUITID': 'BLDG', 'PROP MGR': 'Tiffany Elam', 'LEASID': '000471', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #463 - 1/31/21', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2006, 8, 10), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 1, 31), 'VACATE DATE': datetime.date(2021, 1, 31), 'EXPIRE DATE': datetime.date(2021, 1, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0466', 'SUITID': 'BLDG', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '001237', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Carquest Auto Parts - Sold Bldg 12/15/23', 'GEN CODE': 'TER', 'RENT START': datetime.date(2019, 7, 20), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2023, 12, 15), 'VACATE DATE': datetime.date(2023, 12, 15), 'EXPIRE DATE': datetime.date(2024, 7, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0509', 'SUITID': 'A', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '001305', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #509-10/19/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 31), 'VACATE DATE': datetime.date(2020, 10, 19), 'EXPIRE DATE': datetime.date(2020, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0525', 'SUITID': 'A', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '000744', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Snap Fitness 24/7 - Bldg Sold', 'GEN CODE': 'TER', 'RENT START': datetime.date(2011, 5, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 7, 31), 'VACATE DATE': datetime.date(2020, 7, 31), 'EXPIRE DATE': datetime.date(2024, 9, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0525', 'SUITID': 'B', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '000545', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #525 - 7/21/20', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2008, 2, 8), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 7, 31), 'VACATE DATE': datetime.date(2020, 7, 21), 'EXPIRE DATE': datetime.date(2020, 7, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0525', 'SUITID': 'C', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '000758', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Little Caesars - Bldg Sold', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2011, 10, 10), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 7, 31), 'VACATE DATE': datetime.date(2020, 7, 31), 'EXPIRE DATE': datetime.date(2021, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0550', 'SUITID': 'A', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '000523', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #550', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2007, 11, 9), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 2, 28), 'VACATE DATE': datetime.date(2020, 2, 28), 'EXPIRE DATE': datetime.date(2020, 2, 28), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0550', 'SUITID': 'B', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '000912', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Cricket Wireless', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2014, 10, 6), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 1, 17), 'VACATE DATE': datetime.date(2020, 1, 27), 'EXPIRE DATE': datetime.date(2020, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0597', 'SUITID': 'A', 'PROP MGR': 'James Park', 'LEASID': '000663', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'MFS Realty, Corroboro, LLC', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2010, 1, 28), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 1, 31), 'VACATE DATE': datetime.date(2020, 1, 31), 'EXPIRE DATE': datetime.date(2020, 1, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0597', 'SUITID': 'A', 'PROP MGR': 'James Park', 'LEASID': '001338', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': "Corroboro, LLC - Marco's", 'GEN CODE': 'REN', 'RENT START': datetime.date(2020, 2, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 1, 28), 'VACATE DATE': datetime.date(2022, 1, 28), 'EXPIRE DATE': datetime.date(2022, 1, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0597', 'SUITID': 'B', 'PROP MGR': 'James Park', 'LEASID': '000652', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Neighborhood Market Tobacco', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2009, 11, 10), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 1, 28), 'VACATE DATE': datetime.date(2022, 1, 28), 'EXPIRE DATE': datetime.date(2022, 1, 28), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0597', 'SUITID': 'C', 'PROP MGR': 'James Park', 'LEASID': '000633', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #597 - 6/30/2020', 'GEN CODE': 'TER', 'RENT START': datetime.date(2009, 6, 7), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 6, 30), 'VACATE DATE': datetime.date(2020, 6, 30), 'EXPIRE DATE': datetime.date(2020, 6, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': '0597', 'SUITID': 'C', 'PROP MGR': 'James Park', 'LEASID': '001433', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Furniture Innovations Inc.', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 11, 30), 'OCCUPANCY DATE': datetime.date(2020, 6, 29), 'STOP BILL DATE': datetime.date(2022, 1, 28), 'VACATE DATE': datetime.date(2022, 1, 28), 'EXPIRE DATE': datetime.date(2025, 11, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0630', 'SUITID': 'A', 'PROP MGR': 'James Park', 'LEASID': '000654', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #630', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2009, 12, 11), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 1, 15), 'VACATE DATE': datetime.date(2021, 1, 15), 'EXPIRE DATE': datetime.date(2021, 1, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0630', 'SUITID': 'B', 'PROP MGR': 'James Park', 'LEASID': '000811', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': "Marco's Pizza #3515", 'GEN CODE': 'NEW', 'RENT START': datetime.date(2013, 4, 21), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 1, 15), 'VACATE DATE': datetime.date(2021, 1, 15), 'EXPIRE DATE': datetime.date(2021, 1, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0656', 'SUITID': 'BLDG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001097', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'International Grocery', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2017, 10, 15), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 3, 31), 'VACATE DATE': datetime.date(2021, 3, 22), 'EXPIRE DATE': datetime.date(2022, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0709', 'SUITID': 'BLDG', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '000686', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #709-10/19/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2010, 7, 30), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 31), 'VACATE DATE': datetime.date(2020, 10, 19), 'EXPIRE DATE': datetime.date(2020, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0738', 'SUITID': 'BLDG', 'PROP MGR': 'James Park', 'LEASID': '000710', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #738', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2010, 10, 16), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 1, 31), 'VACATE DATE': datetime.date(2021, 1, 31), 'EXPIRE DATE': datetime.date(2021, 1, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0752', 'SUITID': 'BLDG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '000712', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #752-10/19/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2010, 10, 28), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 31), 'VACATE DATE': datetime.date(2020, 10, 19), 'EXPIRE DATE': datetime.date(2020, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0812', 'SUITID': 'A', 'PROP MGR': 'Tiffany Elam', 'LEASID': '001244', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Great Clips', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2019, 8, 10), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 3, 16), 'VACATE DATE': datetime.date(2021, 3, 16), 'EXPIRE DATE': datetime.date(2023, 8, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0812', 'SUITID': 'B', 'PROP MGR': 'Tiffany Elam', 'LEASID': '000845', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #812', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2013, 11, 8), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 2, 28), 'VACATE DATE': datetime.date(2021, 2, 28), 'EXPIRE DATE': datetime.date(2021, 2, 28), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0812', 'SUITID': 'KIOSK', 'PROP MGR': 'Tiffany Elam', 'LEASID': '001234', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'HPWI  #4316', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2019, 7, 15), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 3, 16), 'VACATE DATE': datetime.date(2021, 3, 16), 'EXPIRE DATE': datetime.date(2021, 3, 16), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0822', 'SUITID': 'A', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '001128', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'World Finance Corporation', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2018, 2, 18), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 8, 30), 'VACATE DATE': datetime.date(2021, 8, 30), 'EXPIRE DATE': datetime.date(2021, 8, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '0822', 'SUITID': 'C', 'PROP MGR': 'Nathalie Gurber', 'LEASID': '001014', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video #822-10/26/2020', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2016, 9, 16), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 10, 31), 'VACATE DATE': datetime.date(2020, 10, 26), 'EXPIRE DATE': datetime.date(2020, 10, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '5015', 'SUITID': 'CORP', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '000089', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Video Corporate', 'GEN CODE': 'NEW', 'RENT START': datetime.date(1982, 12, 28), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 6, 11), 'VACATE DATE': datetime.date(2021, 6, 11), 'EXPIRE DATE': datetime.date(2021, 6, 11), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '5015', 'SUITID': 'DEPUY', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '000916', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'DePuy Sythes Sales', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2014, 11, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 2, 28), 'VACATE DATE': datetime.date(2021, 2, 28), 'EXPIRE DATE': datetime.date(2021, 2, 28), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '5016', 'SUITID': 'SHOP', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '001378', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Springfield Home Office Shop', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 6, 11), 'VACATE DATE': datetime.date(2021, 6, 11), 'EXPIRE DATE': datetime.date(2021, 6, 11), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': '5017', 'SUITID': 'WRHSE', 'PROP MGR': 'Clayton Vandeventer', 'LEASID': '001379', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Springfield Home Office Warehouse', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 6, 11), 'VACATE DATE': datetime.date(2021, 6, 11), 'EXPIRE DATE': datetime.date(2021, 6, 11), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past; Expire Date Past', 'BLDGID': '5020', 'SUITID': 'BLDG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001326', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2104 - Redwood/Asheville NC', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 3, 5), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2022, 7, 21), 'EXPIRE DATE': datetime.date(2022, 7, 21), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': '5020', 'SUITID': 'BLDG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002212', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Heartland Vet - Redwood/Asheville SOLD', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 7, 22), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2023, 11, 9), 'VACATE DATE': datetime.date(2023, 11, 9), 'EXPIRE DATE': datetime.date(2034, 7, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past; Expire Date Past', 'BLDGID': '5025', 'SUITID': 'BLDG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001635', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2112 - Ormond Beach FL', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2021, 1, 8), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2022, 7, 21), 'EXPIRE DATE': datetime.date(2022, 7, 21), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past', 'BLDGID': '5025', 'SUITID': 'BLDG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002217', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Heartland Vet - Ormond Beach - SOLD', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 7, 22), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2023, 12, 15), 'EXPIRE DATE': datetime.date(2034, 7, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '094OH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001946', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Holistic Health - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 6), 'VACATE DATE': datetime.date(2022, 2, 6), 'EXPIRE DATE': datetime.date(2022, 2, 6), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '154GO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002062', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GLP Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269AU', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001953', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Audacious OH - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 1), 'VACATE DATE': datetime.date(2022, 6, 1), 'EXPIRE DATE': datetime.date(2022, 6, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269BC', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001996', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'B Cubed Ops - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269BV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002048', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Buzzed Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269CG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001985', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Community Greenhouse Ohio', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 6, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 15), 'VACATE DATE': datetime.date(2022, 6, 15), 'EXPIRE DATE': datetime.date(2022, 6, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269CV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002012', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Cannvitz Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269EH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002006', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Empowered Healing - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269GB', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002054', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GreenBud Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269HF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002018', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Higher Feeling - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269HT', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002030', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'HealingThruCannabis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269IO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002024', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Inspired Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269LO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002000', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Loomin - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269MM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001978', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'MariMed - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 12), 'VACATE DATE': datetime.date(2022, 4, 12), 'EXPIRE DATE': datetime.date(2022, 4, 12), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269PO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002042', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Pristine Oasis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past', 'BLDGID': 'OHCANN', 'SUITID': '269SZ', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002036', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Sizzle Piqua - Contingent 9/30/2022', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2022, 9, 19), 'EXPIRE DATE': datetime.date(2032, 6, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279AU', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001952', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Audacious OH - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 3, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 1), 'VACATE DATE': datetime.date(2022, 6, 1), 'EXPIRE DATE': datetime.date(2022, 6, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279BC', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001997', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'B Cubed Ops - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279BR', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002070', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Bridge City Collective - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 13), 'VACATE DATE': datetime.date(2022, 5, 13), 'EXPIRE DATE': datetime.date(2022, 5, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279BV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002049', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Buzzed Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279CG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001983', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Community Greenhouse Ohio', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 6, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 15), 'VACATE DATE': datetime.date(2022, 6, 15), 'EXPIRE DATE': datetime.date(2022, 6, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279CV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002013', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Cannvitz Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279EH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002007', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Empowered Healing - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279GB', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002055', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GreenBud Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279GF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001991', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GF Ohio, LLC- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 15), 'VACATE DATE': datetime.date(2022, 2, 15), 'EXPIRE DATE': datetime.date(2022, 2, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279GM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001987', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GLD Management - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 1), 'VACATE DATE': datetime.date(2022, 2, 1), 'EXPIRE DATE': datetime.date(2022, 2, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279GO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002063', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GLP Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279HF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002019', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Higher Feeling - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279HT', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002031', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'HealingThruCannabis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279IO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002025', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Inspired Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279JP', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001956', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Joshua Pursuit Equities- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 19), 'VACATE DATE': datetime.date(2022, 4, 19), 'EXPIRE DATE': datetime.date(2022, 4, 19), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279LM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001942', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'LMTT - Terminated 4/8', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 8), 'VACATE DATE': datetime.date(2022, 4, 8), 'EXPIRE DATE': datetime.date(2022, 4, 8), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279LO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002001', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Loomin - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279MM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001977', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'MariMed Tiffin - CONTINGENT 8/31/22', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 3, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 11, 16), 'VACATE DATE': datetime.date(2022, 11, 16), 'EXPIRE DATE': datetime.date(2032, 5, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279NA', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001969', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Natures OH LLC - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 27), 'VACATE DATE': datetime.date(2022, 4, 27), 'EXPIRE DATE': datetime.date(2022, 4, 27), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279NM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001990', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Nectar Markets of Ohio- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 15), 'VACATE DATE': datetime.date(2022, 2, 15), 'EXPIRE DATE': datetime.date(2022, 2, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279OH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001947', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Holistic Health- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 6), 'VACATE DATE': datetime.date(2022, 2, 6), 'EXPIRE DATE': datetime.date(2022, 2, 6), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279OV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001961', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Valley MMD - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 30), 'VACATE DATE': datetime.date(2022, 4, 30), 'EXPIRE DATE': datetime.date(2022, 4, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279PO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002043', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Pristine Oasis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279SW', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001973', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Standard Wellness- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 4), 'VACATE DATE': datetime.date(2022, 2, 4), 'EXPIRE DATE': datetime.date(2022, 2, 4), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '279SZ', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002037', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Sizzle - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283AU', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001954', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Audacious - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 8, 29), 'VACATE DATE': datetime.date(2022, 8, 29), 'EXPIRE DATE': datetime.date(2022, 8, 29), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283BC', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001998', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'B Cubed Ops  - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283BR', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002067', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Bridge City Collective - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 13), 'VACATE DATE': datetime.date(2022, 5, 13), 'EXPIRE DATE': datetime.date(2022, 5, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283BV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002050', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Buzzed Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283CG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001980', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Community Greenhouse Ohio', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 6, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 15), 'VACATE DATE': datetime.date(2022, 6, 15), 'EXPIRE DATE': datetime.date(2022, 6, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283CL', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001968', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Curaleaf OGT, Inc - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 3), 'VACATE DATE': datetime.date(2022, 5, 3), 'EXPIRE DATE': datetime.date(2022, 5, 3), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283CV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002014', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Cannvitz Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283EH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002008', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Empowered Healing - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283FS', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001964', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Firelands Scientific - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 2), 'VACATE DATE': datetime.date(2022, 6, 2), 'EXPIRE DATE': datetime.date(2022, 6, 2), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283GB', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002056', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GreenBud Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283GF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001993', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GF Ohio, LLC- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 15), 'VACATE DATE': datetime.date(2022, 2, 15), 'EXPIRE DATE': datetime.date(2022, 2, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283GO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002065', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GLP Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283HF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002020', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Higher Feeling - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283HT', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002032', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Healing Springfield - CONTINGENT 9/30/2022', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2022, 9, 19), 'EXPIRE DATE': datetime.date(2032, 6, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283IO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002026', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Inspired Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283JP', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001958', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Joshua Pursuit Equities- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 19), 'VACATE DATE': datetime.date(2022, 4, 19), 'EXPIRE DATE': datetime.date(2022, 4, 19), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283JU', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001939', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Jushi - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 3), 'VACATE DATE': datetime.date(2022, 5, 3), 'EXPIRE DATE': datetime.date(2022, 5, 3), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283LM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001943', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'LMTT, LLC - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 4), 'VACATE DATE': datetime.date(2022, 2, 4), 'EXPIRE DATE': datetime.date(2022, 2, 4), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283LO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002002', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Loomin - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283MM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001974', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'MariMed - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 12), 'VACATE DATE': datetime.date(2022, 4, 12), 'EXPIRE DATE': datetime.date(2022, 4, 12), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283NA', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001970', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Natures OH LLC - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 27), 'VACATE DATE': datetime.date(2022, 4, 27), 'EXPIRE DATE': datetime.date(2022, 4, 27), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283NM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001988', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Nectar Markets of Ohio- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 15), 'VACATE DATE': datetime.date(2022, 2, 15), 'EXPIRE DATE': datetime.date(2022, 2, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283OV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001959', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Valley MMD - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 30), 'VACATE DATE': datetime.date(2022, 4, 30), 'EXPIRE DATE': datetime.date(2022, 4, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283PO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002044', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Pristine Oasis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283SO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001986', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Sublime OH - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 1), 'VACATE DATE': datetime.date(2022, 2, 1), 'EXPIRE DATE': datetime.date(2022, 2, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283SZ', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002038', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Sizzle - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '283TO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002061', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Trulieve', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 5, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 31), 'VACATE DATE': datetime.date(2022, 5, 31), 'EXPIRE DATE': datetime.date(2022, 5, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350AU', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001955', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Audacious OH - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 3, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 1), 'VACATE DATE': datetime.date(2022, 6, 1), 'EXPIRE DATE': datetime.date(2022, 6, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350BR', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002068', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Bridge City Collective - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 13), 'VACATE DATE': datetime.date(2022, 5, 13), 'EXPIRE DATE': datetime.date(2022, 5, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350BV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002051', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Buzzed Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350CD', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001936', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'CNDev, LLC - CONTINGENT 6/17/22', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 1), 'VACATE DATE': datetime.date(2022, 6, 1), 'EXPIRE DATE': datetime.date(2022, 6, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350CG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001984', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Community Greenhouse Ohio', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 6, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 1), 'VACATE DATE': datetime.date(2022, 6, 1), 'EXPIRE DATE': datetime.date(2022, 6, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350CL', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001966', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Curaleaf OGT, Inc - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 3), 'VACATE DATE': datetime.date(2022, 5, 3), 'EXPIRE DATE': datetime.date(2022, 5, 3), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350CV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002015', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Cannvitz Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350EH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002009', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Empowered Healing - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350FS', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001965', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Firelands Scientific - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 2), 'VACATE DATE': datetime.date(2022, 6, 2), 'EXPIRE DATE': datetime.date(2022, 6, 2), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350GB', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002057', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GreenBud Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350GO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002064', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GLP Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350HF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002021', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Higher Feeling - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350HT', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002033', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'HealingThruCannabis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350IO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002027', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Inspired Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350JU', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001940', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Jushi - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 3), 'VACATE DATE': datetime.date(2022, 5, 3), 'EXPIRE DATE': datetime.date(2022, 5, 3), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350LO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002003', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Loomin - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350MM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001975', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'MariMed - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 12), 'VACATE DATE': datetime.date(2022, 4, 12), 'EXPIRE DATE': datetime.date(2022, 4, 12), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350OV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001962', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Valley MMD - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 30), 'VACATE DATE': datetime.date(2022, 4, 30), 'EXPIRE DATE': datetime.date(2022, 4, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350PO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002045', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Pristine Oasis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350SZ', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002039', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Sizzle - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350TO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002060', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Trulieve', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 5, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 1), 'VACATE DATE': datetime.date(2022, 6, 1), 'EXPIRE DATE': datetime.date(2032, 4, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '350VE', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001972', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Verano / E Parker LLC - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 19), 'VACATE DATE': datetime.date(2022, 4, 19), 'EXPIRE DATE': datetime.date(2022, 4, 19), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439AU', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001950', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Audacious OH - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 3, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 1), 'VACATE DATE': datetime.date(2022, 6, 1), 'EXPIRE DATE': datetime.date(2022, 6, 1), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439BC', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001995', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'B Cubed Ops - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439BV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002052', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Buzzed Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439CG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001982', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Community Greenhouse Ohio', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 6, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 6, 15), 'VACATE DATE': datetime.date(2022, 6, 15), 'EXPIRE DATE': datetime.date(2022, 6, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439CV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002016', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Cannvitz Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439EH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002010', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Empowered Healing - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439GB', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002058', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GreenBud Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439GF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001992', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GF Ohio, LLC- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 15), 'VACATE DATE': datetime.date(2022, 2, 15), 'EXPIRE DATE': datetime.date(2022, 2, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439GO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002066', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GLP Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439HF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002022', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Higher Feeling - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439HT', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002034', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'HealingThruCannabis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439IO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002028', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Inspired Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439JP', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001957', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Joshua Pursuit Equities- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 19), 'VACATE DATE': datetime.date(2022, 4, 19), 'EXPIRE DATE': datetime.date(2022, 4, 19), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439LM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001944', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'LMTT- Terminated 4/8', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 8), 'VACATE DATE': datetime.date(2022, 4, 8), 'EXPIRE DATE': datetime.date(2022, 4, 8), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439LO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002004', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Loomin - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439MM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001976', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'MariMed - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 12), 'VACATE DATE': datetime.date(2022, 4, 12), 'EXPIRE DATE': datetime.date(2022, 4, 12), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439NA', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001971', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Natures OH LLC - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 27), 'VACATE DATE': datetime.date(2022, 4, 27), 'EXPIRE DATE': datetime.date(2022, 4, 27), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439NM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001989', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Nectar Markets of Ohio- Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 15), 'VACATE DATE': datetime.date(2022, 2, 15), 'EXPIRE DATE': datetime.date(2022, 2, 15), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439OH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001948', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'OhioHolHealth - CONTINGENT 10/31/2022', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 11, 7), 'VACATE DATE': datetime.date(2022, 9, 19), 'EXPIRE DATE': datetime.date(2032, 5, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439OV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001963', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Valley MMD - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 30), 'VACATE DATE': datetime.date(2022, 4, 30), 'EXPIRE DATE': datetime.date(2022, 4, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439PO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002046', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Pristine Oasis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '439SZ', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002040', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Sizzle - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '503CD', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001937', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'CNDev, LLC - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 3, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 16), 'VACATE DATE': datetime.date(2022, 5, 16), 'EXPIRE DATE': datetime.date(2022, 5, 16), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531AU', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001951', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Audacious OH - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 3, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 20), 'VACATE DATE': datetime.date(2022, 4, 20), 'EXPIRE DATE': datetime.date(2022, 4, 20), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531BC', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001994', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'B Cubed Ops - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531BR', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002069', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Bridge City Collective - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 13), 'VACATE DATE': datetime.date(2022, 5, 13), 'EXPIRE DATE': datetime.date(2022, 5, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531BV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002053', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Buzzed Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531CD', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001938', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'CNDev, LLC - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 3, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 16), 'VACATE DATE': datetime.date(2022, 5, 16), 'EXPIRE DATE': datetime.date(2022, 5, 16), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531CG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001981', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Community Greenhouse Ohio', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 20), 'VACATE DATE': datetime.date(2022, 4, 20), 'EXPIRE DATE': datetime.date(2022, 4, 20), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531CL', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001967', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Curaleaf OGT, Inc - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 5, 3), 'VACATE DATE': datetime.date(2022, 5, 3), 'EXPIRE DATE': datetime.date(2022, 5, 3), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531CV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002017', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Cannvitz Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531EH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002011', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Empowered Healing - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531GB', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002059', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'GreenBud Ventures - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531HF', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002023', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Higher Feeling - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531HT', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002035', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'HealingThruCannabis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531IO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002029', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Inspired Ohio - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531LM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001945', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'LMTT, LLC - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 4), 'VACATE DATE': datetime.date(2022, 2, 4), 'EXPIRE DATE': datetime.date(2022, 2, 4), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531LO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002005', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Loomin - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531MM', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001979', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'MariMed - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 12), 'VACATE DATE': datetime.date(2022, 4, 12), 'EXPIRE DATE': datetime.date(2022, 4, 12), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531OG', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001941', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Griz - CONTINGENT 4/30/2022', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 20), 'VACATE DATE': datetime.date(2022, 4, 20), 'EXPIRE DATE': datetime.date(2032, 3, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531OH', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001949', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Holistic Health', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 2, 6), 'VACATE DATE': datetime.date(2022, 2, 6), 'EXPIRE DATE': datetime.date(2022, 2, 6), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531OV', 'PROP MGR': 'Jennifer Ray', 'LEASID': '001960', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Ohio Valley MMD - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 30), 'VACATE DATE': datetime.date(2022, 4, 30), 'EXPIRE DATE': datetime.date(2022, 4, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531PO', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002047', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Pristine Oasis - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'OHCANN', 'SUITID': '531SZ', 'PROP MGR': 'Jennifer Ray', 'LEASID': '002041', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Sizzle - Terminated', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2022, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 4, 13), 'VACATE DATE': datetime.date(2022, 4, 13), 'EXPIRE DATE': datetime.date(2022, 4, 13), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2101', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '000027', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2101 - Oaklandon FV', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2019, 7, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 6, 7), 'VACATE DATE': datetime.date(2020, 6, 7), 'EXPIRE DATE': datetime.date(2019, 12, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2102', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '000028', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2102 - Stonebridge/McKinney TX', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2019, 9, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 7, 21), 'VACATE DATE': datetime.date(2022, 7, 21), 'EXPIRE DATE': datetime.date(2023, 5, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2104', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001344', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2104', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 1, 1), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 3, 5), 'VACATE DATE': datetime.date(2020, 3, 5), 'EXPIRE DATE': datetime.date(2020, 12, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2105', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001415', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2105 - Sugar Creek/Crawfordsville IN', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 3, 3), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 1, 20), 'VACATE DATE': datetime.date(2022, 1, 20), 'EXPIRE DATE': datetime.date(2021, 9, 3), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2106', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001416', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2106 - Crawfordsville', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 3, 4), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2020, 6, 30), 'VACATE DATE': datetime.date(2020, 5, 31), 'EXPIRE DATE': datetime.date(2020, 5, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2107', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001434', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2107 - Bedford Oaks', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 5, 4), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2021, 1, 22), 'VACATE DATE': datetime.date(2021, 1, 22), 'EXPIRE DATE': datetime.date(2020, 12, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2110', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001522', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2110 - Canyon Creek/Sherman TX', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 8, 10), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 7, 22), 'VACATE DATE': datetime.date(2022, 7, 22), 'EXPIRE DATE': datetime.date(2024, 8, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2113', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001581', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2113 - Avalon Park/Orlando FL', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 10, 9), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 7, 22), 'VACATE DATE': datetime.date(2022, 7, 22), 'EXPIRE DATE': datetime.date(2022, 7, 22), 'TENANT CATEGORY': None}, {'ISSUE': 'Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2114', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001643', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet Group #2114', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2021, 1, 26), 'OCCUPANCY DATE': None, 'STOP BILL DATE': None, 'VACATE DATE': datetime.date(2021, 3, 31), 'EXPIRE DATE': datetime.date(2021, 3, 31), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2115', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001623', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2115 - Bellshire/Nashville TN', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2020, 12, 28), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 7, 22), 'VACATE DATE': datetime.date(2022, 7, 22), 'EXPIRE DATE': datetime.date(2022, 11, 30), 'TENANT CATEGORY': None}, {'ISSUE': 'Stop Bill Date Past; Vacate Date Past; Expire Date Past', 'BLDGID': 'RNTFVG', 'SUITID': '2118', 'PROP MGR': 'Legacy Commercial Property', 'LEASID': '001927', 'OCCUPANCY STATUS': 'I', 'TENANT NAME': 'Family Vet #2118 - Highland Sq/Lakeland FL', 'GEN CODE': 'NEW', 'RENT START': datetime.date(2021, 10, 20), 'OCCUPANCY DATE': None, 'STOP BILL DATE': datetime.date(2022, 7, 22), 'VACATE DATE': datetime.date(2022, 7, 22), 'EXPIRE DATE': datetime.date(2022, 9, 30), 'TENANT CATEGORY': None}]
Diagnostic - Total leases: [{'TOTAL_LEASES': 205}]
Diagnostic - Current leases: [{'CURRENT_LEASES': 205}]
